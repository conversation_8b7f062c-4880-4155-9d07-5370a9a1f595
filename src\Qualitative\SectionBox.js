import { Accordion, AccordionTab } from 'primereact/accordion';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Checkbox } from 'primereact/checkbox';
import { Dropdown } from 'primereact/dropdown';
import { InputNumber } from 'primereact/inputnumber';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { RadioButton } from 'primereact/radiobutton';
import React, { useEffect, useState } from 'react';
import APIServices from '../service/APIService';
import { API } from '../constants/api_url';

const SectionBox = ({ data, onUpdateResponse, userlist }) => {

  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(data.type === 'checkbox-group' ? [] : '');
  const [fieldName, setFieldName] = useState(null)
  const [consolidateText, setConsolidateText] = useState('');
  const getUser = (id) => {
    let user_name = 'Not Found'
    let index = userlist.findIndex(i => i.id === Number(id))
    if (index !== -1) {
      user_name = userlist[index].information.empname
    }
    return user_name
  }



  const frameworkMap = {
    2: "GRI",
    3: "ISSB",
    4: "MCfS",
    5: "SASB",
    6: "TCFD",
    7: "BRSR",
    8: "SGX",
    9: "Boursa Kuwait",
    10: "Bursa Malaysia",
    11: "HKEX",
    12: "NASDAQ",
    13: "CDP",
    14: "EcoVadis",
    15: "CDP",
    16: "EcoVadis",
    17: "MSCI",
    18: "S&P Dow Jones",
    19: "Sustainalitics",
    20: "ISS",
    21: "Carbon Footprint",
    22: "GEF capital",
    24: "BRSR Core",
    25: "CSRD",
    26: "DJSI",
    27: "ESRS",
    28: "IFRS S1",
    29: "IFRS S2",
  };
  const defaultUser = 'Consolidate';
  // 👇 Mapping function
  const getDisplayType = (type) => {
    const typeMap = {
      text: 'descriptive',
      textarea: 'descriptive',
      checkbox: 'multi-select',
      'checkbox-group': 'multi-select',
      radio: 'single-select',
      'radio-group': 'single-select',
      'select-single': 'single-select',
      'select-multi': 'multi-select',
      file: 'file-upload',
      number: 'numeric',
      date: 'date',
      'datetime-local': 'date',
    };
    return typeMap[type] || type;
  };
  useEffect(() => {
    const consolidate = Object.entries(data?.response || {}).flatMap(([a, b]) => b)?.find(x => x.user === 'Consolidate') || {}
    if (consolidate.answer != null) {
      if (['select', 'radio-group', 'checkpoint'].includes(data.type)) {

        consolidate.res = data.values?.find(x => x.label === consolidate.answer)?.value
      } else if (data.type === 'checkbox-group') {
        consolidate.res = data.values?.filter(x => consolidate?.answer?.includes(x.label)).map(x => x.value)
      } else {
        consolidate.res = consolidate.answer

      }
    }
    setConsolidateText(consolidate.comment)
    console.log(consolidate)
    setInputValue(consolidate ? consolidate?.res : (data.type === 'checkbox-group' ? [] : ''))
  }, [data])


  const displayType = getDisplayType(data.type);

  const getTagColor = (type) => {
    const display = getDisplayType(type);
    switch (display) {
      case 'single-select':
      case 'checkpoint':
        return ['#dbf7d7', '#61be53'];
      case 'file-upload':
        return ['#f4d8e9', '#8a276e'];
      case 'multi-select':
        return ['#cce5ff', '#004085'];
      case 'numeric':
        return ['#fbe8a6', '#a67c00'];
      default:
        return ['#f4e9c4', '#e99e34'];
    }
  };

  const handleSave = (question) => {
    let finalAnswer = inputValue;

    if (['select', 'radio-group', 'checkpoint'].includes(data.type)) {
      const selected = data.values?.find(opt => opt.label === inputValue || opt.value === inputValue);
      finalAnswer = selected?.label || '';
    }

    if (data.type === 'checkbox-group') {
      finalAnswer = inputValue.filter(val =>
        data.values?.some(opt => opt.label === val || opt.value === val)
      );
    }
    console.log(finalAnswer, question.name)
    const newResponse = {
      user: defaultUser,
      answer: finalAnswer,
    };

    const filtered = (data.response || []).filter(r => r.user !== defaultUser);
    const updatedResponses = [...filtered, newResponse, {
      user: defaultUser,
      answer: consolidateText,
    }];

    onUpdateResponse({
      ...(finalAnswer != null && finalAnswer !== '' ? { [data.name]: finalAnswer } : {}),
      [`${data.name}_comments`]: consolidateText
    });


    setInputValue(data.type === 'checkbox-group' ? [] : '');
    setConsolidateText('');
  };

  const handleCancel = () => {
    setInputValue(data.type === 'checkbox-group' ? [] : '');
    setConsolidateText('');
  };

  const [bg, color] = getTagColor(data.type);

  const inputTypes = [
    'text', 'textarea', 'number', 'select',
    'radio-group', 'checkbox-group', 'file',
    'date', 'checkpoint'
  ];

  const responsesToShow = (data.response && data.response.length > 0)
    ? data.response
    : []

  const renderAnswer = (answer) => {

    if (data.type === 'file') {
      return <a href={answer?.includes('api.eisqr.com') ? answer : API.Docs + answer} >{answer} </a>
    }
    if (Array.isArray(answer)) {
      return answer
        .map(val => data.values?.find(opt => opt.value === val || opt.label === val)?.label || val)
        .filter(Boolean)
        .join(', ');
    }
    return data.values?.find(opt => opt.value === answer || opt.label === answer)?.label || answer;
  };

  if (data.type === 'paragraph') return null;

  return (


    <Accordion>
      <AccordionTab
        className='acc-pad'
        header={
          <div className="col-12 flex justify-content-between parent-full-width">
            <div className='col-9'>{data.label}</div>
            <div className='col-3 flex' >
              <span
                className="badge d-inline-block text-capitalize"
                style={{
                  backgroundColor: ['#f4e9c4', '#e99e34'],
                  color,
                  marginLeft: 'auto',
                  fontSize: '0.75rem',
                  padding: '6px 12px',
                  borderRadius: '5px',
                  minWidth: '100px',
                  textAlign: 'center'
                }}
              >
                {data.name}
              </span>
              <span
                className="badge d-inline-block text-capitalize"
                style={{
                  backgroundColor: bg,
                  color,
                  marginLeft: 'auto',
                  fontSize: '0.75rem',
                  padding: '6px 12px',
                  borderRadius: '5px',
                  minWidth: '100px',
                  textAlign: 'center'
                }}
              >
                {displayType}
              </span>

            </div>
          </div>
        }
      >
        <div className="accordion-body">
          <div className='flex justify-content-end m-2'>
            {data.assignedFramework && (
              <div className="d-flex flex-wrap gap-2 mt-2">
                {Object.entries(data.assignedFramework).map(([frameworkId, labels]) =>
                  labels.map((labelVal, i) => (
                    <span
                      key={`${frameworkId}-${i}`}
                      className="badge bg-primary text-white px-2 py-1 rounded"
                      style={{ fontSize: "0.85rem" }}
                    >
                      {frameworkMap[frameworkId]} - {labelVal}
                    </span>
                  ))
                )}
              </div>
            )}
          </div>
          {/* Response Table */}
          <div className="table-responsive mb-3">
            <table className="table table-bordered">
              <thead className="table-light">
                <tr>
                  <th>From</th>
                  <th>Entity</th>
                  <th>Response</th>
                </tr>
              </thead>
              <tbody>
                {responsesToShow.map((row, idx) =>
                  row.user !== 'Consolidate' ? (
                    <tr key={idx}>
                      <td>{getUser(Number(row.user))}</td>
                      <td>{row.entity}</td>
                      <td>{renderAnswer(row.answer)}</td>
                    </tr>
                  ) : null
                )}
              </tbody>
            </table>
          </div>
  <div className="table-responsive mb-3">
            <table className="table table-bordered">
              <thead className="table-light">
                <tr>
                  <th>Consolidated Response</th>
                </tr>
              </thead>
              <tbody>
                {responsesToShow.map((row, idx) =>
                  row.user === 'Consolidate' ? (
                    <tr key={idx}>
                 
                      <td>{renderAnswer(row.answer)}</td>
                    </tr>
                  ) : null
                )}
              </tbody>
            </table>
          </div>

          {/* Input Label */}
          {inputTypes.includes(data.type) && (
            <label className="form-label fw-bold">Enter Consilidated Response</label>
          )}

          {/* Input Field */}
          <div className="mb-3">
            {data.type === 'text' && (
              <InputText className='col-5'
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
              />
            )}
            {data.type === 'textarea' && (
              <InputTextarea className='col-5'
                rows={3}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
              />
            )}
            {data.type === 'number' && (
              <InputNumber className='col-5'
                min={0}

                value={inputValue}
                onChange={(e) => setInputValue(e.value)}
              />
            )}
            {data.type === 'date' && (
              <Calendar className='col-5'

                value={inputValue}
                onChange={(e) => setInputValue(e.value)}
              />
            )}
            {data.type === 'file' && (
              <input
                className="form-control"
                type="file"
                onChange={(e) => { const form = new FormData(); form.append('file', e.target.files[0]); APIServices.post(API.FilesUpload, form).then((res) => { setInputValue(res?.data?.files?.[0]?.originalname) }) }}
              />
            )}
            {data.type === 'select' && (
              <Dropdown className='col-5'
                value={
                  inputValue
                }
                options={data?.values || []}
                onChange={(e) => {
                  setInputValue(e?.value || null);
                }}
              />
            )}
            {['radio-group', 'checkpoint'].includes(data.type) && (
              <div>
                {data.values.map((cb, cbind) => {
                  return (
                    <div className="p-2 flex text-justify fs-14 fw-5 align-items-center" >
                      <RadioButton inputId={"rg" + cbind} name={cb.label} value={cb.value} onChange={(e) => setInputValue(e.value)} checked={inputValue === cb.value} />

                      <label htmlFor={"rg" + cbind} className="ml-2">{cb.label}</label>
                    </div>
                  )
                })}

              </div>
            )}
            {data.type === 'checkbox-group' && (
              <div>
                {data.values.map((cb, cbind) => {
                  console.log(inputValue)
                  return (
                    <div className="flex text-justify fs-14 fw-5" style={{ marginBottom: 10 }}>
                      <Checkbox inputId={"cb" + cbind} name={cb.label} value={cb.value} onChange={() => {
                        const val = cb.value;
                        setInputValue(prev => {
                          if (!Array.isArray(prev)) prev = [];
                          return prev.includes(val)
                            ? prev.filter(v => v !== val)
                            : [...prev, val];
                        });
                      }} checked={Array.isArray(inputValue) && inputValue.includes(cb.value)} />
                      <label htmlFor={"cb" + cbind} className="ml-2">{cb.label}</label>
                    </div>
                  )
                })

                }

              </div>
            )}
          </div>

          {/* Comment Section */}
          <div className="mb-2 mt-3">
            <label className="form-label fw-bold">Comment / Remarks</label>
            <textarea
              className="form-control"
              rows={3}
              value={consolidateText}
              onChange={(e) => setConsolidateText(e.target.value)}
            />
          </div>

          {/* Buttons */}
          <div className="d-flex gap-2">
            <Button

              label='Save'
              onClick={() => { handleSave(data) }}
            />

            <Button
              text
              className='mandatory'
              onClick={handleCancel}
              label='Cancel'
            />

          </div>

        </div>
      </AccordionTab>
    </Accordion>
  );
};

export default SectionBox;
